import 'package:get/get.dart';

/// Base interface for all initializable services
abstract class IInitializableService extends GetxService {
  /// Initialize the service asynchronously
  Future<void> initialize();

  /// Ensure the service is initialized (idempotent)
  Future<dynamic> ensureInitialized();

  /// Check if the service is initialized
  bool get isInitialized;

  /// Check if the service is ready for use
  bool get isReady;
}

/// Interface for Localization service
abstract class ILocalizationService extends IInitializableService {
  /// Get current locale
  dynamic get currentLocale;

  /// Change locale
  Future<void> changeLocale(dynamic locale);

  /// Toggle between supported languages
  Future<void> toggleLanguage();

  /// Get text direction for current locale
  dynamic get textDirection;

  /// Check if current locale is RTL
  bool get isRTL;

  /// Get supported locales
  List<dynamic> get supportedLocales;
}

/// Service initialization interface
abstract class IServiceInitializer {
  /// Initialize all application services
  Future<void> initializeServices();

  /// Get initialization order
  List<Type> get initializationOrder;

  /// Check if all services are initialized
  bool get allServicesInitialized;
}
